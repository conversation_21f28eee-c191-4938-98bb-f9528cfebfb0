<script setup lang="ts">
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { Button } from '@/components/ui/button'
import Icon from '@/components/Icon.vue'

const activeRole = ref('admin')
const roles = ref<any[]>([])
const permissions = ref([
  { id: 'users', name: '<PERSON>uản lý người dùng', view: true, add: true, edit: true, delete: true, approve: true, export: true },
  { id: 'roles', name: 'Quản lý phân quyền', view: true, add: true, edit: true, delete: true, approve: true, export: true },
  { id: 'map', name: 'Quản lý bản đồ', view: true, add: true, edit: true, delete: true, approve: true, export: true },
  { id: 'search', name: '<PERSON>uản lý tìm kiếm', view: true, add: false, edit: false, delete: false, approve: false, export: true },
  { id: 'reports', name: '<PERSON><PERSON><PERSON><PERSON> lý bá<PERSON> c<PERSON>', view: true, add: true, edit: true, delete: true, approve: true, export: true },
  { id: 'data', name: '<PERSON><PERSON><PERSON><PERSON> lý cập nhật dữ liệu', view: true, add: true, edit: true, delete: true, approve: true, export: true },
  { id: 'settings', name: 'Quản lý cài đặt hệ thống', view: true, add: true, edit: true, delete: true, approve: true, export: true },
])
const loading = ref(false)
const error = ref('')

const fetchRoles = async () => {
  loading.value = true
  try {
    const res = await axios.get('/api/admin/roles')
    roles.value = res.data.data || res.data
    if (roles.value.length > 0) {
      activeRole.value = roles.value[0].name
    }
  } catch (e: any) {
    error.value = 'Không thể tải danh sách vai trò.'
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchRoles()
})
</script>

<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold">
        Quản Lý Phân Quyền
      </h2>
      <Button>
        <Icon name="Plus" class="h-4 w-4 mr-2" />
        Thêm vai trò
      </Button>
    </div>
    <div v-if="loading" class="p-4 text-center text-gray-500">Đang tải...</div>
    <div v-else-if="error" class="p-4 text-center text-red-500">{{ error }}</div>
    <div v-else class="flex items-center gap-2 mb-4">
      <Button
        v-for="role in roles"
        :key="role.id"
        :variant="activeRole === role.name ? 'default' : 'outline'"
        @click="activeRole = role.name"
      >
        {{ role.vi_name || role.name }}
      </Button>
    </div>

    <div class="border rounded-md">
      <table class="w-full">
        <thead>
          <tr class="border-b">
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground w-[30%]">
              CHỨC NĂNG
            </th>
            <th class="p-3 text-center text-sm font-semibold text-muted-foreground">
              XEM
            </th>
            <th class="p-3 text-center text-sm font-semibold text-muted-foreground">
              THÊM
            </th>
            <th class="p-3 text-center text-sm font-semibold text-muted-foreground">
              SỬA
            </th>
            <th class="p-3 text-center text-sm font-semibold text-muted-foreground">
              XÓA
            </th>
            <th class="p-3 text-center text-sm font-semibold text-muted-foreground">
              PHÊ DUYỆT
            </th>
            <th class="p-3 text-center text-sm font-semibold text-muted-foreground">
              XUẤT BÁO CÁO
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="perm in permissions" :key="perm.id" class="border-b last:border-b-0">
            <td class="p-3 font-medium">
              {{ perm.name }}
            </td>
            <td class="p-3 text-center">
              <input v-model="perm.view" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary">
            </td>
            <td class="p-3 text-center">
              <input v-model="perm.add" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary">
            </td>
            <td class="p-3 text-center">
              <input v-model="perm.edit" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary">
            </td>
            <td class="p-3 text-center">
              <input v-model="perm.delete" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary">
            </td>
            <td class="p-3 text-center">
              <input v-model="perm.approve" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary">
            </td>
            <td class="p-3 text-center">
              <input v-model="perm.export" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary">
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="flex justify-end mt-6">
      <Button>
        <Icon name="Save" class="h-4 w-4 mr-2" />
        Lưu thay đổi
      </Button>
    </div>
  </div>
</template>
