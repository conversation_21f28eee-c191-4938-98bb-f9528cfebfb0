<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('action', 100); // Loại hoạt động: login, create, update, delete, etc.
            $table->text('description'); // <PERSON>ô tả chi tiết hoạt động
            $table->string('subject_type')->nullable(); // Model class name (optional)
            $table->unsignedBigInteger('subject_id')->nullable(); // Model ID (optional)
            $table->ipAddress('ip_address')->nullable(); // Địa chỉ IP
            $table->text('user_agent')->nullable(); // Thông tin trình duyệt/thiết bị
            $table->json('properties')->nullable(); // <PERSON><PERSON> liệu bổ sung (JSON)
            $table->timestamps();

            // Indexes for better performance
            $table->index(['user_id', 'created_at']);
            $table->index(['action', 'created_at']);
            $table->index(['subject_type', 'subject_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_logs');
    }
};
