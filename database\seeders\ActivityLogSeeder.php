<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ActivityLog;
use App\Models\User;
use Carbon\Carbon;

class ActivityLogSeeder extends Seeder
{
    public function run(): void
    {
        $users = User::all();
        
        if ($users->isEmpty()) {
            $this->command->warn('No users found. Please run RolePermissionSeeder first.');
            return;
        }

        $activities = [
            [
                'action' => 'login',
                'description' => 'Đăng nhập thành công vào hệ thống',
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            ],
            [
                'action' => 'create',
                'description' => 'Thêm mới tài sản "Đập <PERSON><PERSON><PERSON>" (C-00131)',
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:119.0) Gecko/20100101 Firefox/119.0',
            ],
            [
                'action' => 'update',
                'description' => 'Cập nhật thông tin tài sản "Trạm bơm Liên Mạc" (T-00125)',
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            ],
            [
                'action' => 'approve',
                'description' => 'Phê duyệt thêm mới tài sản "Đập Tuyển Quang" (C-00131)',
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
            ],
            [
                'action' => 'reject',
                'description' => 'Từ chối cập nhật thông tin tài sản "Kênh Đồng Cửu Long" (K-00129)',
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
            ],
            [
                'action' => 'delete',
                'description' => 'Xóa tài sản "Trạm bơm Thanh Hà" (T-00128)',
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0',
            ],
            [
                'action' => 'export',
                'description' => 'Xuất báo cáo danh mục TSKCHTLL Quý II/2025',
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            ],
            [
                'action' => 'logout',
                'description' => 'Đăng xuất khỏi hệ thống',
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:119.0) Gecko/20100101 Firefox/119.0',
            ],
        ];

        foreach ($activities as $index => $activity) {
            $user = $users->random();
            $createdAt = Carbon::now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59));
            
            ActivityLog::create([
                'user_id' => $user->id,
                'action' => $activity['action'],
                'description' => $activity['description'],
                'ip_address' => $activity['ip_address'],
                'user_agent' => $activity['user_agent'],
                'created_at' => $createdAt,
                'updated_at' => $createdAt,
            ]);
        }

        // Create some additional random activities
        for ($i = 0; $i < 20; $i++) {
            $user = $users->random();
            $actions = ['login', 'logout', 'view', 'update', 'create'];
            $action = $actions[array_rand($actions)];
            
            $descriptions = [
                'login' => 'Đăng nhập thành công vào hệ thống',
                'logout' => 'Đăng xuất khỏi hệ thống',
                'view' => 'Xem danh sách tài sản',
                'update' => 'Cập nhật thông tin tài sản',
                'create' => 'Tạo mới tài sản',
            ];
            
            $createdAt = Carbon::now()->subDays(rand(0, 60))->subHours(rand(0, 23))->subMinutes(rand(0, 59));
            
            ActivityLog::create([
                'user_id' => $user->id,
                'action' => $action,
                'description' => $descriptions[$action],
                'ip_address' => '192.168.1.' . rand(100, 200),
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'created_at' => $createdAt,
                'updated_at' => $createdAt,
            ]);
        }

        $this->command->info('Activity logs seeded successfully.');
    }
}
