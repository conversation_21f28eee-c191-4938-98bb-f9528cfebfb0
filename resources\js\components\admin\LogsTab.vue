<script setup lang="ts">
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import Icon from '@/components/Icon.vue'

const activityLogs = ref<any[]>([])
const loading = ref(false)
const error = ref('')

const fetchActivityLogs = async () => {
  loading.value = true
  try {
    const res = await axios.get('/api/admin/activity-logs')
    activityLogs.value = res.data.data || res.data
  } catch (e: any) {
    error.value = 'Không thể tải lịch sử hoạt động.'
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchActivityLogs()
})

const getActionClass = (action: string) => {
  switch (action) {
    case 'Đăng nhập':
    case 'Tạo mới':
    case 'Cập nhật':
    case 'Phê duyệt':
      return 'bg-blue-100 text-blue-800'
    case 'Từ chối':
    case 'Xóa':
      return 'bg-red-100 text-red-800'
    case 'Xuất báo cáo':
      return 'bg-purple-100 text-purple-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}
</script>

<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold">
        Lịch Sử Hoạt Động
      </h2>
      <div class="flex items-center gap-2">
        <Button variant="outline">
          <Icon name="Download" class="h-4 w-4 mr-2" />
          Xuất Excel
        </Button>
        <Button>
          <Icon name="RefreshCw" class="h-4 w-4 mr-2" />
          Làm mới
        </Button>
      </div>
    </div>
    <div class="flex items-center gap-4 mb-4 p-4 bg-muted/50 rounded-lg">
      <div class="grid gap-1.5">
        <label class="text-sm font-medium">Từ ngày</label>
        <Input type="date" class="w-40" />
      </div>
      <div class="grid gap-1.5">
        <label class="text-sm font-medium">Đến ngày</label>
        <Input type="date" class="w-40" />
      </div>
      <div class="grid gap-1.5">
        <label class="text-sm font-medium">Người dùng</label>
        <select class="border rounded px-2 py-1.5 w-48">
          <option>Tất cả người dùng</option>
        </select>
      </div>
      <div class="grid gap-1.5">
        <label class="text-sm font-medium">Loại hoạt động</label>
        <select class="border rounded px-2 py-1.5 w-48">
          <option>Tất cả hoạt động</option>
        </select>
      </div>
      <div class="self-end">
        <Button>
          <Icon name="Filter" class="h-4 w-4 mr-2" />
          Lọc
        </Button>
      </div>
    </div>
    <div v-if="loading" class="p-4 text-center text-gray-500">Đang tải...</div>
    <div v-else-if="error" class="p-4 text-center text-red-500">{{ error }}</div>
    <div v-else class="border rounded-md">
      <table class="w-full">
        <thead>
          <tr class="border-b">
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              THỜI GIAN
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              NGƯỜI DÙNG
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              HOẠT ĐỘNG
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              MÔ TẢ
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              ĐỊA CHỈ IP
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              THIẾT BỊ
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(log, index) in activityLogs" :key="index" class="border-b last:border-b-0">
            <td class="p-3 text-sm">
              {{ log.time }}
            </td>
            <td class="p-3 font-medium">
              {{ log.user }}
            </td>
            <td class="p-3">
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="getActionClass(log.action)"
              >
                {{ log.action }}
              </span>
            </td>
            <td class="p-3 text-sm">
              {{ log.description }}
            </td>
            <td class="p-3 text-sm">
              {{ log.ip }}
            </td>
            <td class="p-3 text-sm">
              {{ log.device }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="flex justify-between items-center mt-4 text-sm text-muted-foreground">
      <span>Hiển thị 1 đến 8 của 8 bản ghi</span>
    </div>
  </div>
</template>
