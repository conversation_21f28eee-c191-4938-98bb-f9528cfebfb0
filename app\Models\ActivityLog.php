<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ActivityLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'action',
        'description',
        'subject_type',
        'subject_id',
        'ip_address',
        'user_agent',
        'properties',
    ];

    protected $casts = [
        'properties' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that performed the activity.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subject model that the activity was performed on.
     */
    public function subject(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope to filter by action.
     */
    public function scopeAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Get formatted device information from user agent.
     */
    public function getDeviceAttribute()
    {
        if (!$this->user_agent) {
            return 'Unknown';
        }

        // Simple user agent parsing
        $userAgent = $this->user_agent;
        
        // Browser detection
        $browser = 'Unknown';
        if (preg_match('/Chrome\/([0-9.]+)/', $userAgent, $matches)) {
            $browser = 'Chrome ' . explode('.', $matches[1])[0] . '.0';
        } elseif (preg_match('/Firefox\/([0-9.]+)/', $userAgent, $matches)) {
            $browser = 'Firefox ' . explode('.', $matches[1])[0] . '.0';
        } elseif (preg_match('/Safari\/([0-9.]+)/', $userAgent, $matches)) {
            $browser = 'Safari ' . explode('.', $matches[1])[0] . '.0';
        } elseif (preg_match('/Edge\/([0-9.]+)/', $userAgent, $matches)) {
            $browser = 'Edge ' . explode('.', $matches[1])[0] . '.0';
        }

        // OS detection
        $os = 'Unknown';
        if (preg_match('/Windows NT ([0-9.]+)/', $userAgent, $matches)) {
            $version = $matches[1];
            if ($version == '10.0') $os = 'Windows 10';
            elseif ($version == '6.3') $os = 'Windows 8.1';
            elseif ($version == '6.2') $os = 'Windows 8';
            elseif ($version == '6.1') $os = 'Windows 7';
            else $os = 'Windows';
        } elseif (strpos($userAgent, 'Mac OS X') !== false) {
            if (preg_match('/Mac OS X ([0-9_]+)/', $userAgent, $matches)) {
                $version = str_replace('_', '.', $matches[1]);
                $os = 'macOS ' . $version;
            } else {
                $os = 'macOS';
            }
        } elseif (strpos($userAgent, 'Ubuntu') !== false) {
            $os = 'Ubuntu';
        } elseif (strpos($userAgent, 'Android') !== false) {
            if (preg_match('/Android ([0-9.]+)/', $userAgent, $matches)) {
                $os = 'Android ' . $matches[1];
            } else {
                $os = 'Android';
            }
        } elseif (strpos($userAgent, 'iPhone') !== false || strpos($userAgent, 'iPad') !== false) {
            $os = 'iOS';
        }

        return $browser . ' / ' . $os;
    }

    /**
     * Static method to log activity.
     */
    public static function log(string $action, string $description, $subject = null, array $properties = [])
    {
        $request = request();
        
        return static::create([
            'user_id' => auth()->id(),
            'action' => $action,
            'description' => $description,
            'subject_type' => $subject ? get_class($subject) : null,
            'subject_id' => $subject ? $subject->getKey() : null,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'properties' => $properties,
        ]);
    }
}
