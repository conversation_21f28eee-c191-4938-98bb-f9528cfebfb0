<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{
    /**
     * Get users list with roles and permissions.
     */
    public function getUsers(Request $request): JsonResponse
    {
        try {
            $query = User::with(['roles.permissions']);

            // Search filter
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'ILIKE', "%{$search}%")
                      ->orWhere('email', 'ILIKE', "%{$search}%");
                });
            }

            // Role filter
            if ($request->filled('role')) {
                $query->whereHas('roles', function ($q) use ($request) {
                    $q->where('name', $request->role);
                });
            }

            // Status filter (assuming we have a status field)
            if ($request->filled('status')) {
                // For now, we'll assume all users are active
                // You can add a status field to users table later
            }

            // Pagination
            $perPage = $request->get('per_page', 15);
            $users = $query->latest()->paginate($perPage);

            // Transform data
            $users->getCollection()->transform(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'username' => explode('@', $user->email)[0], // Extract username from email
                    'roles' => $user->roles->map(function ($role) {
                        return [
                            'id' => $role->id,
                            'name' => $role->name,
                            'vi_name' => $role->vi_name ?? $role->name,
                        ];
                    }),
                    'role' => $user->roles->first() ? [
                        'name' => $user->roles->first()->vi_name ?? $user->roles->first()->name
                    ] : null,
                    'permissions' => $user->getAllPermissions()->map(function ($permission) {
                        return [
                            'id' => $permission->id,
                            'name' => $permission->name,
                            'vi_name' => $permission->vi_name ?? $permission->name,
                        ];
                    }),
                    'unit' => $user->unit ?? 'Chưa xác định', // Add unit field if needed
                    'status' => 'Đang hoạt động', // Default status
                    'created_at' => $user->created_at,
                    'updated_at' => $user->updated_at,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $users->items(),
                'meta' => [
                    'current_page' => $users->currentPage(),
                    'last_page' => $users->lastPage(),
                    'per_page' => $users->perPage(),
                    'total' => $users->total(),
                    'from' => $users->firstItem(),
                    'to' => $users->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể lấy danh sách người dùng.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get roles list with permissions.
     */
    public function getRoles(Request $request): JsonResponse
    {
        try {
            $roles = Role::with('permissions')->get();

            $rolesData = $roles->map(function ($role) {
                return [
                    'id' => $role->id,
                    'name' => $role->name,
                    'vi_name' => $role->vi_name ?? $role->name,
                    'permissions' => $role->permissions->map(function ($permission) {
                        return [
                            'id' => $permission->id,
                            'name' => $permission->name,
                            'vi_name' => $permission->vi_name ?? $permission->name,
                        ];
                    }),
                    'users_count' => $role->users()->count(),
                    'created_at' => $role->created_at,
                    'updated_at' => $role->updated_at,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $rolesData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể lấy danh sách vai trò.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get permissions list.
     */
    public function getPermissions(Request $request): JsonResponse
    {
        try {
            $permissions = Permission::all();

            $permissionsData = $permissions->map(function ($permission) {
                return [
                    'id' => $permission->id,
                    'name' => $permission->name,
                    'vi_name' => $permission->vi_name ?? $permission->name,
                    'created_at' => $permission->created_at,
                    'updated_at' => $permission->updated_at,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $permissionsData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể lấy danh sách quyền.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get activity logs.
     */
    public function getActivityLogs(Request $request): JsonResponse
    {
        try {
            $query = ActivityLog::with('user');

            // Date range filter
            if ($request->filled('start_date') && $request->filled('end_date')) {
                $query->dateRange($request->start_date, $request->end_date);
            }

            // User filter
            if ($request->filled('user_id')) {
                $query->byUser($request->user_id);
            }

            // Action filter
            if ($request->filled('action')) {
                $query->action($request->action);
            }

            // Search filter
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('description', 'ILIKE', "%{$search}%")
                      ->orWhere('action', 'ILIKE', "%{$search}%");
                });
            }

            // Pagination
            $perPage = $request->get('per_page', 15);
            $logs = $query->latest()->paginate($perPage);

            // Transform data
            $logs->getCollection()->transform(function ($log) {
                return [
                    'id' => $log->id,
                    'time' => $log->created_at->format('d/m/Y H:i:s'),
                    'user' => $log->user ? $log->user->name : 'Hệ thống',
                    'action' => $this->translateAction($log->action),
                    'description' => $log->description,
                    'ip' => $log->ip_address,
                    'device' => $log->device,
                    'created_at' => $log->created_at,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $logs->items(),
                'meta' => [
                    'current_page' => $logs->currentPage(),
                    'last_page' => $logs->lastPage(),
                    'per_page' => $logs->perPage(),
                    'total' => $logs->total(),
                    'from' => $logs->firstItem(),
                    'to' => $logs->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể lấy lịch sử hoạt động.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get system settings.
     */
    public function getSettings(): JsonResponse
    {
        try {
            // Get settings from cache or database
            $settings = Cache::remember('system_settings', 3600, function () {
                return [
                    'general' => [
                        'systemName' => config('app.name', 'Hệ Thống Quản Lý Cơ Sở Dữ Liệu Tài Sản Kết Cấu Hạ Tầng Thủy Lợi'),
                        'managingUnit' => 'Bộ Nông nghiệp và Phát triển Nông thôn',
                        'contactEmail' => '<EMAIL>',
                        'supportPhone' => '1900 1234',
                    ],
                    'map' => [
                        'defaultBasemap' => 'Google Maps',
                        'defaultLon' => '105.8342',
                        'defaultLat' => '21.0278',
                        'defaultZoom' => 6,
                        'allow3D' => true,
                    ],
                    'security' => [
                        'sessionTimeout' => 30,
                        'minPasswordLength' => 8,
                        'maxLoginAttempts' => 5,
                        'require2FA' => false,
                    ],
                    'report' => [
                        'defaultFormat' => 'Excel',
                        'reportTitle' => 'BÁO CÁO TÀI SẢN KẾT CẤU HẠ TẦNG THỦY LỢI',
                        'reportFooter' => '© Bộ Nông nghiệp và Phát triển Nông thôn',
                        'showLogo' => true,
                    ],
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $settings
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể lấy cài đặt hệ thống.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update system settings.
     */
    public function updateSettings(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'general.systemName' => 'required|string|max:255',
                'general.managingUnit' => 'required|string|max:255',
                'general.contactEmail' => 'required|email|max:255',
                'general.supportPhone' => 'required|string|max:20',
                'map.defaultBasemap' => 'required|string|in:Google Maps,Mapbox,OpenStreetMap',
                'map.defaultLon' => 'required|numeric|between:-180,180',
                'map.defaultLat' => 'required|numeric|between:-90,90',
                'map.defaultZoom' => 'required|integer|between:1,20',
                'map.allow3D' => 'required|boolean',
                'security.sessionTimeout' => 'required|integer|min:5|max:1440',
                'security.minPasswordLength' => 'required|integer|min:6|max:50',
                'security.maxLoginAttempts' => 'required|integer|min:1|max:10',
                'security.require2FA' => 'required|boolean',
                'report.defaultFormat' => 'required|string|in:Excel,PDF,Word',
                'report.reportTitle' => 'required|string|max:255',
                'report.reportFooter' => 'required|string|max:255',
                'report.showLogo' => 'required|boolean',
            ]);

            $settings = $request->all();

            // Here you would typically save to database
            // For now, we'll just update the cache
            Cache::put('system_settings', $settings, 3600);

            // Log the activity
            ActivityLog::log(
                'update',
                'Cập nhật cài đặt hệ thống',
                null,
                ['settings' => $settings]
            );

            return response()->json([
                'success' => true,
                'message' => 'Cài đặt đã được cập nhật thành công.',
                'data' => $settings
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ.',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể cập nhật cài đặt hệ thống.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Translate action to Vietnamese.
     */
    private function translateAction(string $action): string
    {
        $translations = [
            'login' => 'Đăng nhập',
            'logout' => 'Đăng xuất',
            'create' => 'Tạo mới',
            'update' => 'Cập nhật',
            'delete' => 'Xóa',
            'approve' => 'Phê duyệt',
            'reject' => 'Từ chối',
            'export' => 'Xuất báo cáo',
            'import' => 'Nhập dữ liệu',
            'view' => 'Xem',
        ];

        return $translations[$action] ?? ucfirst($action);
    }
}
